export type Role = 'ADMIN' | 'BROKER' | 'AGENT' | 'BUYER'
export type Action = 'listing:create' | 'listing:update' | 'listing:read'

export type Membership = { orgId: string; role: Role }
export type Subject = { id: string; memberships: Membership[] }

export type ListingRes = { kind: 'listing'; orgId: string; ownerId?: string }

export function roleInOrg(user: Subject, orgId: string) {
  return user.memberships.find((m) => m.orgId === orgId)?.role
}

export function can(user: Subject, action: Action, res: ListingRes): boolean {
  const r = roleInOrg(user, res.orgId)
  if (!r) return false
  if (action === 'listing:create')
    return r === 'ADMIN' || r === 'BROKER' || r === 'AGENT'
  if (action === 'listing:update')
    return (
      r === 'ADMIN' ||
      r === 'BROKER' ||
      (r === 'AGENT' && res.ownerId === user.id)
    )
  if (action === 'listing:read') return true
  return false
}

export function authorize(user: Subject, action: Action, res: ListingRes) {
  if (!can(user, action, res)) {
    const e: any = new Error('FORBIDDEN')
    e.status = 403
    throw e
  }
}
