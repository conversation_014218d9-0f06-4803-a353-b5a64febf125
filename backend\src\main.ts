import { NestFactory } from '@nestjs/core'
import { ValidationPipe, Logger } from '@nestjs/common'
import { AppModule } from './app.module'

async function bootstrap() {
	const app = await NestFactory.create(AppModule)
	const logger = new Logger('Bootstrap')

	// Global validation pipe
	app.useGlobalPipes(
		new ValidationPipe({
			whitelist: true,
			forbidNonWhitelisted: true,
			transform: true
		})
	)

	// Enable CORS
	app.enableCors({
		origin: process.env.FRONTEND_URL || 'http://localhost:3000',
		credentials: true
	})

	const port = process.env.PORT ?? 3001
	await app.listen(port)
	logger.log(`Application is running on: http://localhost:${port}`)
}

bootstrap().catch(error => {
	console.error('Failed to start application:', error)
	process.exit(1)
})
