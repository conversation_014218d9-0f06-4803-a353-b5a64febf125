import React from 'react'

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  helperText?: string
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  helperText,
  className = '',
  id,
  ...props
}) => {
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`
  
  const inputClasses = `
    w-full px-3 py-2 border rounded-md shadow-sm placeholder-muted
    focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent
    transition-colors
    ${error 
      ? 'border-destructive focus:ring-destructive' 
      : 'border-border bg-input hover:border-muted focus:border-primary'
    }
    ${className}
  `.trim()

  return (
    <div className="space-y-1">
      {label && (
        <label 
          htmlFor={inputId}
          className="block text-sm font-medium text-foreground"
        >
          {label}
        </label>
      )}
      <input
        id={inputId}
        className={inputClasses}
        aria-invalid={error ? 'true' : 'false'}
        aria-describedby={error ? `${inputId}-error` : helperText ? `${inputId}-helper` : undefined}
        {...props}
      />
      {error && (
        <p 
          id={`${inputId}-error`}
          className="text-sm text-destructive"
          role="alert"
        >
          {error}
        </p>
      )}
      {helperText && !error && (
        <p 
          id={`${inputId}-helper`}
          className="text-sm text-muted"
        >
          {helperText}
        </p>
      )}
    </div>
  )
}
