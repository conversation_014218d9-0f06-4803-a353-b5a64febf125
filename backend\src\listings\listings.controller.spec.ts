import { Test, TestingModule } from '@nestjs/testing'
import { JwtService } from '@nestjs/jwt'
import { ConfigModule } from '@nestjs/config'
import { ListingsController } from './listings.controller'
import { PrismaService } from '../prisma/prisma.service'
import { JwtAuthGuard } from '../auth/jwt-auth.guard'
import { PolicyGuard } from '../auth/policy.guard'
import { CreateListingDto } from './dto/create-listing.dto'

describe('ListingsController', () => {
	let controller: ListingsController
	let prismaService: PrismaService

	const mockPrismaService = {
		listing: {
			create: jest.fn()
		},
		membership: {
			findMany: jest.fn()
		}
	}

	const mockJwtService = {
		verifyAsync: jest.fn()
	}

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			imports: [ConfigModule.forRoot()],
			controllers: [ListingsController],
			providers: [
				{
					provide: PrismaService,
					useValue: mockPrismaService
				},
				{
					provide: JwtService,
					useValue: mockJwtService
				},
				JwtAuthGuard,
				PolicyGuard
			]
		}).compile()

		controller = module.get<ListingsController>(ListingsController)
		prismaService = module.get<PrismaService>(PrismaService)
	})

	it('should be defined', () => {
		expect(controller).toBeDefined()
	})

	describe('create', () => {
		it('should create a listing', async () => {
			const dto: CreateListingDto = {
				orgId: 'org-1',
				title: 'Test Listing',
				price: 100000
			}

			const user = {
				id: 'user-1',
				memberships: [{ orgId: 'org-1', role: 'AGENT' as const }]
			}

			const expectedResult = {
				id: 'listing-1',
				...dto,
				ownerId: user.id
			}

			mockPrismaService.listing.create.mockResolvedValue(expectedResult)

			const result = await controller.create(dto, { user })

			expect(result).toEqual(expectedResult)
			expect(mockPrismaService.listing.create).toHaveBeenCalledWith({
				data: { ...dto, ownerId: user.id }
			})
		})
	})
})
