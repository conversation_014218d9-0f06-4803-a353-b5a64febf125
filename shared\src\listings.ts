import { z } from 'zod'
import { authorize, Subject } from './iam'

export const CreateListingInput = z.object({
  orgId: z.string().min(1),
  title: z.string().min(3),
  price: z.number().int().nonnegative()
})
export type CreateListingInput = z.infer<typeof CreateListingInput>

export function makeCreateListing(deps: { prisma: any }) {
  return async (user: Subject, input: CreateListingInput) => {
    authorize(user, 'listing:create', { kind: 'listing', orgId: input.orgId })
    return deps.prisma.listing.create({ data: { ...input, ownerId: user.id } })
  }
}
