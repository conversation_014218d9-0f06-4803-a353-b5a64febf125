'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { LoginForm } from '@/components/auth/LoginForm'
import { SignupForm } from '@/components/auth/SignupForm'

type AuthMode = 'login' | 'signup'

export default function AuthPage() {
  const [mode, setMode] = useState<AuthMode>('login')
  const router = useRouter()

  const handleAuthSuccess = () => {
    // Redirect to dashboard or home page after successful authentication
    router.push('/')
  }

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo/Brand Section */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">MRH</h1>
          <p className="text-muted">Multi-tenant Real Estate Management</p>
        </div>

        {/* Authentication Forms */}
        {mode === 'login' ? (
          <LoginForm onSuccess={handleAuthSuccess} onSwitchToSignup={() => setMode('signup')} />
        ) : (
          <SignupForm onSuccess={handleAuthSuccess} onSwitchToLogin={() => setMode('login')} />
        )}

        {/* Footer */}
        <div className="mt-8 text-center">
          <p className="text-xs text-muted">By continuing, you agree to our Terms of Service and Privacy Policy</p>
        </div>
      </div>
    </div>
  )
}
