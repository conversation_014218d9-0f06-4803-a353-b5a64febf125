'use server'

import { CreateListingInput, makeCreateListing, Subject } from '@mrh/shared'
import { prisma } from '@/lib/prisma'
import { auth } from '@/auth' // NextAuth

export async function createListingAction(formData: FormData) {
  const session = await auth()
  const uid = session?.user?.id
  if (!uid) throw Object.assign(new Error('UNAUTHORIZED'), { status: 401 })

  const input = CreateListingInput.parse({
    orgId: String(formData.get('orgId')),
    title: String(formData.get('title')),
    price: Number(formData.get('price'))
  })

  const memberships = await prisma.membership.findMany({
    where: { userId: uid },
    select: { orgId: true, role: true }
  })

  const subject: Subject = { id: uid, memberships }
  return makeCreateListing({ prisma })(subject, input)
}
