{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:migrate:reset": "prisma migrate reset", "db:studio": "prisma studio", "db:seed": "prisma db seed"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@mrh/shared": "file:../shared", "@nestjs/config": "^4.0.2", "@nestjs/jwt": "^11.0.0", "@prisma/client": "^6.13.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "next": "15.4.5", "next-auth": "^4.24.11", "postcss": "^8.5.6", "prisma": "^6.13.0", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4.1.11", "typescript": "^5"}}