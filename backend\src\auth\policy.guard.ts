import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common'
import { authorize, Subject } from '@mrh/shared'
import type { Request } from 'express'

@Injectable()
export class PolicyGuard implements CanActivate {
  canActivate(ctx: ExecutionContext): boolean {
    const req = ctx.switchToHttp().getRequest<Request & { user: Subject }>()
    const user: Subject = req.user // set by your JWT auth guard
    const { orgId, ownerId } = (req.body as { orgId?: string; ownerId?: string }) ?? {}

    try {
      authorize(user, 'listing:create', {
        kind: 'listing',
        orgId: String(orgId),
        ownerId: ownerId ? String(ownerId) : undefined
      })
      return true
    } catch {
      // authorize() throws an error when authorization fails
      return false
    }
  }
}
