import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { AppController } from './app.controller'
import { AppService } from './app.service'
import { PrismaModule } from './prisma/prisma.module'
import { AuthModule } from './auth/auth.module'
import { ListingsModule } from './listings/listings.module'
import { JwtAuthGuard } from './auth/jwt-auth.guard'
import { PolicyGuard } from './auth/policy.guard'

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env'
    }),
    PrismaModule,
    AuthModule,
    ListingsModule
  ],
  controllers: [AppController],
  providers: [AppService, JwtAuthGuard, PolicyGuard]
})
export class AppModule {}
