import { dirname } from 'path'
import { fileURLToPath } from 'url'
import { FlatCompat } from '@eslint/eslintrc'
import prettierPlugin from 'eslint-plugin-prettier'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const compat = new FlatCompat({
  baseDirectory: __dirname
})

const eslintConfig = [
  ...compat.extends('next/core-web-vitals', 'next/typescript'),
  {
    plugins: {
      prettier: prettierPlugin
    },
    rules: {
      'prettier/prettier': [
        'error',
        {
          // Prettier formatting options
          singleQuote: true,
          semi: false,
          tabWidth: 2,
          trailingComma: 'none',
          printWidth: 120,
          useTabs: false,
          bracketSpacing: true,
          arrowParens: 'avoid',
          endOfLine: 'lf'
        },
        {
          // Plugin options
          usePrettierrc: true,
          fileInfoOptions: {
            withNodeModules: false
          }
        }
      ]
    }
  }
]

export default eslintConfig
