{"name": "@mrh/shared", "version": "0.1.0", "private": true, "type": "module", "description": "", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup src/index.ts --dts --format cjs,esm --out-dir dist", "dev": "tsup src/index.ts --dts --format cjs,esm --out-dir dist --watch", "prepare": "npm run build"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"tsup": "^8.5.0", "typescript": "^5.9.2", "zod": "^4.0.14"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}}