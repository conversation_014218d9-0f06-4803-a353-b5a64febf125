"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/page",{

/***/ "(app-pages-browser)/./src/components/auth/SignupForm.tsx":
/*!********************************************!*\
  !*** ./src/components/auth/SignupForm.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SignupForm: () => (/* binding */ SignupForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* __next_internal_client_entry_do_not_use__ SignupForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst SignupForm = (param)=>{\n    let { onSuccess, onSwitchToLogin } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: '',\n        password: '',\n        name: ''\n    });\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.email) {\n            newErrors.email = 'Email is required';\n        } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            newErrors.email = 'Please enter a valid email address';\n        }\n        if (!formData.password) {\n            newErrors.password = 'Password is required';\n        } else if (formData.password.length < 8) {\n            newErrors.password = 'Password must be at least 8 characters long';\n        }\n        if (!confirmPassword) {\n            newErrors.confirmPassword = 'Please confirm your password';\n        } else if (formData.password !== confirmPassword) {\n            newErrors.confirmPassword = 'Passwords do not match';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        setLoading(true);\n        setErrors({});\n        try {\n            const response = await authService.signup(formData);\n            authService.setToken(response.access_token);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(response.access_token);\n        } catch (error) {\n            setErrors({\n                general: error instanceof Error ? error.message : 'Signup failed'\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleInputChange = (field)=>(e)=>{\n            setFormData((prev)=>({\n                    ...prev,\n                    [field]: e.target.value\n                }));\n            if (errors[field]) {\n                setErrors((prev)=>({\n                        ...prev,\n                        [field]: undefined\n                    }));\n            }\n        };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"w-full max-w-md mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-center text-foreground\",\n                        children: \"Create Account\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-muted mt-2\",\n                        children: \"Join us to get started\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-4\",\n                        children: [\n                            errors.general && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 bg-destructive/10 border border-destructive/20 rounded-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-destructive\",\n                                    children: errors.general\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                label: \"Full Name (Optional)\",\n                                type: \"text\",\n                                value: formData.name,\n                                onChange: handleInputChange('name'),\n                                placeholder: \"Enter your full name\",\n                                error: errors.name\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                label: \"Email Address\",\n                                type: \"email\",\n                                value: formData.email,\n                                onChange: handleInputChange('email'),\n                                placeholder: \"Enter your email\",\n                                error: errors.email,\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                label: \"Password\",\n                                type: \"password\",\n                                value: formData.password,\n                                onChange: handleInputChange('password'),\n                                placeholder: \"Create a password\",\n                                error: errors.password,\n                                helperText: \"Must be at least 8 characters long\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                label: \"Confirm Password\",\n                                type: \"password\",\n                                value: confirmPassword,\n                                onChange: (e)=>{\n                                    setConfirmPassword(e.target.value);\n                                    if (errors.confirmPassword) {\n                                        setErrors((prev)=>({\n                                                ...prev,\n                                                confirmPassword: undefined\n                                            }));\n                                    }\n                                },\n                                placeholder: \"Confirm your password\",\n                                error: errors.confirmPassword,\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                type: \"submit\",\n                                className: \"w-full\",\n                                loading: loading,\n                                size: \"lg\",\n                                children: \"Create Account\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted\",\n                            children: [\n                                \"Already have an account?\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onSwitchToLogin,\n                                    className: \"text-primary hover:text-primary-dark font-medium transition-colors\",\n                                    children: \"Sign in\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SignupForm, \"HyDh1xduQNavVBi9sgksHKKMu98=\");\n_c = SignupForm;\nvar _c;\n$RefreshReg$(_c, \"SignupForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/SignupForm.tsx\n"));

/***/ })

});